from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Boolean
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String
from app.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    is_agent = Column(Boolean, default=False)