{% extends "base.html" %}
{% block title %}Search Properties | Dream yours{% endblock %}

{% block head %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>

<!-- Custom Map Search CSS -->
<style>
.map-search-container {
    display: flex;
    height: calc(100vh - 80px);
    position: relative;
}

.search-sidebar {
    width: 400px;
    background: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    z-index: 1000;
}

.search-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.search-header h1 {
    margin: 0 0 10px 0;
    font-size: 24px;
    color: #333;
}

.search-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.search-filters {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.filter-row {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
}

.filter-group {
    flex: 1;
}

.filter-group label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #006aff;
    box-shadow: 0 0 0 2px rgba(0, 106, 255, 0.1);
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 16px;
    margin-bottom: 15px;
}

.property-results {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.property-count {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.property-list {
    padding: 0;
}

.property-item {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.property-item:hover {
    background-color: #f8f9fa;
}

.property-item.active {
    background-color: #e3f2fd;
    border-left: 3px solid #006aff;
}

.property-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 10px;
}

.property-price {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.property-details {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.property-address {
    font-size: 13px;
    color: #888;
}

.map-container {
    flex: 1;
    position: relative;
}

#map {
    height: 100%;
    width: 100%;
}

.map-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.map-control-btn {
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.map-control-btn:hover {
    background: #f0f0f0;
}

.boundary-controls {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    background: white;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.boundary-btn {
    background: #006aff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 10px;
}

.boundary-btn:hover {
    background: #0056cc;
}

.boundary-btn.active {
    background: #ff6b35;
}

.clear-boundary-btn {
    background: #666;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.clear-boundary-btn:hover {
    background: #555;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .map-search-container {
        flex-direction: column;
        height: auto;
    }
    
    .search-sidebar {
        width: 100%;
        height: 50vh;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .map-container {
        height: 50vh;
    }
    
    .filter-row {
        flex-direction: column;
        gap: 10px;
    }
}

/* Loading indicator */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 2000;
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #006aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block content %}
<div class="map-search-container">
    <!-- Search Sidebar -->
    <div class="search-sidebar">
        <div class="search-header">
            <h1>Find Your Dream Home</h1>
            <p id="property-count">{{ stats.total_count }} homes available</p>
        </div>
        
        <div class="search-filters">
            <form id="search-form" class="search-form">
                <input type="text" id="location-search" name="q" class="search-input" 
                       placeholder="Enter city, neighborhood, or ZIP code" 
                       value="{{ search_params.q }}">
                
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="listing_type">For Sale/Rent</label>
                        <select id="listing_type" name="listing_type">
                            <option value="">All</option>
                            <option value="sale" {% if search_params.listing_type == 'sale' %}selected{% endif %}>For Sale</option>
                            <option value="rent" {% if search_params.listing_type == 'rent' %}selected{% endif %}>For Rent</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="property_type">Home Type</label>
                        <select id="property_type" name="property_type">
                            <option value="">All Types</option>
                            <option value="house" {% if search_params.property_type == 'house' %}selected{% endif %}>House</option>
                            <option value="condo" {% if search_params.property_type == 'condo' %}selected{% endif %}>Condo</option>
                            <option value="townhouse" {% if search_params.property_type == 'townhouse' %}selected{% endif %}>Townhouse</option>
                            <option value="apartment" {% if search_params.property_type == 'apartment' %}selected{% endif %}>Apartment</option>
                        </select>
                    </div>
                </div>
                
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="min_price">Min Price</label>
                        <input type="number" id="min_price" name="min_price" placeholder="No min" 
                               value="{{ search_params.min_price }}">
                    </div>
                    <div class="filter-group">
                        <label for="max_price">Max Price</label>
                        <input type="number" id="max_price" name="max_price" placeholder="No max" 
                               value="{{ search_params.max_price }}">
                    </div>
                </div>
                
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="bedrooms">Beds</label>
                        <select id="bedrooms" name="bedrooms">
                            <option value="">Any</option>
                            <option value="1" {% if search_params.bedrooms == '1' %}selected{% endif %}>1+</option>
                            <option value="2" {% if search_params.bedrooms == '2' %}selected{% endif %}>2+</option>
                            <option value="3" {% if search_params.bedrooms == '3' %}selected{% endif %}>3+</option>
                            <option value="4" {% if search_params.bedrooms == '4' %}selected{% endif %}>4+</option>
                            <option value="5" {% if search_params.bedrooms == '5' %}selected{% endif %}>5+</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="bathrooms">Baths</label>
                        <select id="bathrooms" name="bathrooms">
                            <option value="">Any</option>
                            <option value="1" {% if search_params.bathrooms == '1' %}selected{% endif %}>1+</option>
                            <option value="1.5" {% if search_params.bathrooms == '1.5' %}selected{% endif %}>1.5+</option>
                            <option value="2" {% if search_params.bathrooms == '2' %}selected{% endif %}>2+</option>
                            <option value="2.5" {% if search_params.bathrooms == '2.5' %}selected{% endif %}>2.5+</option>
                            <option value="3" {% if search_params.bathrooms == '3' %}selected{% endif %}>3+</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="property-results">
            <div class="property-count" id="results-count">
                {{ results_count }} of {{ stats.total_count }} homes
            </div>
            <div class="property-list" id="property-list">
                {% for property in properties %}
                <div class="property-item" data-id="{{ property.id }}" 
                     data-lat="{{ property.latitude }}" data-lng="{{ property.longitude }}">
                    <img src="{{ property.photo_url or '/static/images/placeholder-home.svg' }}"
                         alt="Property image" class="property-image">
                    <div class="property-price">${{ '{:,.0f}'.format(property.price) }}</div>
                    <div class="property-details">
                        {{ property.bedrooms }} beds | {{ property.bathrooms }} baths | {{ '{:,}'.format(property.sqft) }} sqft
                    </div>
                    <div class="property-address">{{ property.address }}, {{ property.city }}, {{ property.state }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
        
        <div class="map-controls">
            <button class="map-control-btn" id="zoom-to-fit">Zoom to Fit</button>
            <button class="map-control-btn" id="toggle-satellite">Satellite</button>
        </div>
        
        <div class="boundary-controls">
            <button class="boundary-btn" id="draw-boundary">Draw Boundary</button>
            <button class="clear-boundary-btn" id="clear-boundary">Remove Boundary</button>
        </div>
        
        <div class="loading-indicator" id="loading-indicator">
            <div class="loading-spinner"></div>
            <div>Loading properties...</div>
        </div>
    </div>
</div>

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<!-- Map Search JavaScript -->
<script src="/static/js/map_search.js" defer></script>
{% endblock %}
