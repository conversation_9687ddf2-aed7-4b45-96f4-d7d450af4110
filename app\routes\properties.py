from fastapi import APIRouter, Depends, Request, Form, Query
from fastapi.responses import RedirectResponse, JSONResponse
from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.crud import property as crud_property
from app.schemas.property import PropertyCreate
from app.models.property import Property
from fastapi.templating import Jinja2Templates
from typing import Optional

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/properties")
def list_properties(request: Request, db: Session = Depends(get_db)):
    properties = crud_property.get_properties(db)
    stats = crud_property.get_property_stats(db)
    return templates.TemplateResponse("property/list.html", {
        "request": request,
        "properties": properties,
        "stats": stats
    })

@router.get("/search")
def search_properties(
    request: Request,
    q: Optional[str] = Query(None, description="Search query"),
    min_price: Optional[float] = Query(None, description="Minimum price"),
    max_price: Optional[float] = Query(None, description="Maximum price"),
    bedrooms: Optional[int] = Query(None, description="Minimum bedrooms"),
    bathrooms: Optional[float] = Query(None, description="Minimum bathrooms"),
    min_sqft: Optional[int] = Query(None, description="Minimum square feet"),
    max_sqft: Optional[int] = Query(None, description="Maximum square feet"),
    city: Optional[str] = Query(None, description="City"),
    state: Optional[str] = Query(None, description="State"),
    zip_code: Optional[str] = Query(None, description="ZIP code"),
    sort_by: Optional[str] = Query("price_asc", description="Sort by"),
    db: Session = Depends(get_db)
):
    properties = crud_property.search_properties(
        db=db,
        query=q,
        min_price=min_price,
        max_price=max_price,
        bedrooms=bedrooms,
        bathrooms=bathrooms,
        min_sqft=min_sqft,
        max_sqft=max_sqft,
        city=city,
        state=state,
        zip_code=zip_code,
        sort_by=sort_by
    )
    stats = crud_property.get_property_stats(db)

    # Prepare search parameters for template
    search_params = {
        'q': q or '',
        'min_price': min_price or '',
        'max_price': max_price or '',
        'bedrooms': bedrooms or '',
        'bathrooms': bathrooms or '',
        'min_sqft': min_sqft or '',
        'max_sqft': max_sqft or '',
        'city': city or '',
        'state': state or '',
        'zip_code': zip_code or '',
        'sort_by': sort_by or 'price_asc'
    }

    return templates.TemplateResponse("property/search.html", {
        "request": request,
        "properties": properties,
        "stats": stats,
        "search_params": search_params,
        "results_count": len(properties)
    })

@router.get("/api/search")
def api_search_properties(
    q: Optional[str] = Query(None),
    min_price: Optional[float] = Query(None),
    max_price: Optional[float] = Query(None),
    bedrooms: Optional[int] = Query(None),
    bathrooms: Optional[float] = Query(None),
    min_sqft: Optional[int] = Query(None),
    max_sqft: Optional[int] = Query(None),
    city: Optional[str] = Query(None),
    state: Optional[str] = Query(None),
    zip_code: Optional[str] = Query(None),
    sort_by: Optional[str] = Query("price_asc"),
    db: Session = Depends(get_db)
):
    """API endpoint for AJAX search requests"""
    properties = crud_property.search_properties(
        db=db,
        query=q,
        min_price=min_price,
        max_price=max_price,
        bedrooms=bedrooms,
        bathrooms=bathrooms,
        min_sqft=min_sqft,
        max_sqft=max_sqft,
        city=city,
        state=state,
        zip_code=zip_code,
        sort_by=sort_by
    )

    # Convert to JSON-serializable format
    results = []
    for prop in properties:
        results.append({
            "id": prop.id,
            "title": prop.title,
            "price": prop.price,
            "bedrooms": prop.bedrooms,
            "bathrooms": prop.bathrooms,
            "sqft": prop.sqft,
            "address": prop.address,
            "city": prop.city,
            "state": prop.state,
            "zip_code": prop.zip_code,
            "description": prop.description,
            "photo_url": prop.photo_url
        })

    return JSONResponse({
        "properties": results,
        "count": len(results)
    })

@router.get("/properties/{property_id}")
def property_detail(property_id: int, request: Request, db: Session = Depends(get_db)):
    property = crud_property.get_property(db, property_id)
    if not property:
        return RedirectResponse("/properties")
    return templates.TemplateResponse("property/detail.html", {"request": request, "property": property})

@router.post("/properties/create")
def create_property(
    title: str = Form(...),
    price: float = Form(...),
    bedrooms: int = Form(...),
    bathrooms: float = Form(...),
    sqft: int = Form(...),
    address: str = Form(...),
    city: str = Form(...),
    state: str = Form(...),
    zip_code: str = Form(...),
    description: str = Form(""),
    photo_url: str = Form(""),
    db: Session = Depends(get_db)
):
    property_in = PropertyCreate(
        title=title,
        price=price,
        bedrooms=bedrooms,
        bathrooms=bathrooms,
        sqft=sqft,
        address=address,
        city=city,
        state=state,
        zip_code=zip_code,
        description=description,
        photo_url=photo_url
    )
    crud_property.create_property(db, property_in)
    return RedirectResponse("/properties", status_code=303)