<svg width="1920" height="800" viewBox="0 0 1920 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4682B4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#8FBC8F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#556B2F;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Sky background -->
  <rect width="100%" height="100%" fill="url(#skyGradient)"/>

  <!-- Mountains in background -->
  <polygon points="0,400 300,200 600,300 900,150 1200,250 1500,180 1920,220 1920,800 0,800" fill="url(#mountainGradient)" opacity="0.6"/>

  <!-- Houses -->
  <!-- House 1 -->
  <g transform="translate(200,450)">
    <polygon points="0,50 50,0 100,50 100,100 0,100" fill="#8B4513"/>
    <rect x="10" y="60" width="80" height="40" fill="#DEB887"/>
    <rect x="20" y="70" width="15" height="15" fill="#4169E1"/>
    <rect x="65" y="70" width="15" height="15" fill="#4169E1"/>
    <rect x="40" y="80" width="20" height="20" fill="#8B4513"/>
  </g>

  <!-- House 2 -->
  <g transform="translate(400,480)">
    <polygon points="0,40 40,0 80,40 80,80 0,80" fill="#A0522D"/>
    <rect x="8" y="50" width="64" height="30" fill="#F5DEB3"/>
    <rect x="15" y="60" width="12" height="12" fill="#4169E1"/>
    <rect x="53" y="60" width="12" height="12" fill="#4169E1"/>
    <rect x="32" y="65" width="16" height="15" fill="#8B4513"/>
  </g>

  <!-- House 3 -->
  <g transform="translate(600,460)">
    <polygon points="0,60 60,0 120,60 120,120 0,120" fill="#CD853F"/>
    <rect x="12" y="70" width="96" height="50" fill="#FFEFD5"/>
    <rect x="25" y="85" width="18" height="18" fill="#4169E1"/>
    <rect x="77" y="85" width="18" height="18" fill="#4169E1"/>
    <rect x="48" y="95" width="24" height="25" fill="#8B4513"/>
  </g>

  <!-- Trees -->
  <g transform="translate(100,500)">
    <rect x="15" y="40" width="10" height="40" fill="#8B4513"/>
    <circle cx="20" cy="35" r="25" fill="#228B22"/>
  </g>

  <g transform="translate(800,520)">
    <rect x="12" y="30" width="8" height="30" fill="#8B4513"/>
    <circle cx="16" cy="25" r="20" fill="#32CD32"/>
  </g>

  <!-- Clouds -->
  <g opacity="0.8">
    <ellipse cx="300" cy="100" rx="40" ry="20" fill="white"/>
    <ellipse cx="320" cy="90" rx="50" ry="25" fill="white"/>
    <ellipse cx="340" cy="100" rx="35" ry="18" fill="white"/>
  </g>

  <g opacity="0.7">
    <ellipse cx="1200" cy="120" rx="45" ry="22" fill="white"/>
    <ellipse cx="1225" cy="110" rx="55" ry="28" fill="white"/>
    <ellipse cx="1250" cy="120" rx="40" ry="20" fill="white"/>
  </g>

  <!-- Overlay for text readability -->
  <rect width="100%" height="100%" fill="rgba(0,0,0,0.3)"/>
</svg>